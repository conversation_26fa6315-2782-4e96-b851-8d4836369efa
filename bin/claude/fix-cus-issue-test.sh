#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/issue-test-$1/repo ]; then
  mkdir -p ~/claude_workdirs/issue-test-$1/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/issue-test-$1/repo
  cd ~/claude_workdirs/issue-test-$1/repo
  git checkout -b feature/test-$1 || true
  git branch --set-upstream-to=origin/main feature/test-$1
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cd $src_dir
cp .mcp.json ~/claude_workdirs/issue-test-$1/repo/.mcp.json
mkdir -p ~/claude_workdirs/issue-test-$1/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/issue-test-$1/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/issue-test-$1/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/issue-test-$1/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/issue-test-$1/repo
id=$1
cd ..
"$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions  "

Please read apps/customer/tests/CLUADE.md for details on writing tests.

Please fix the test apps/customer/tests/issues/issue-eko-$1.spec.ts

Firstly look at a working test such as apps/customer/tests/editor-ai-features.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

You're doing a great job, keep at it, plan this out and ultra think carefully step by and work your way through this methodically. Be your best self!
"
export PW_PORT=${2:-3333}
# Loop while tests are failing
count=0
mkdir -p ~/claude_workdirs/issue-test-$1/test-results/${count}/
until (cd ~/claude_workdirs/issue-test-$1/repo/apps/customer/ && npx playwright test --reporter=line --output=~/claude_workdirs/issue-test-$1/test-results/${count}/ tests/"$id".spec.ts) > .last-test-run || [ $count -eq 10 ]
do
  ((count++))
  cd ~/claude_workdirs/issue-test-$1
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions  -c "The tests are still failing in @apps/customer/tests/$id.spec.ts, please fix this. The log file is in .last-test-run. The test result files are in /workspace/test-results/${count}/ Please fix these failing tests"
done

if (( count < 10))
then
  cd  ~/claude_workdirs/issue-test-$1/repo
  "$src_dir"/bin/claude-docker  issue-test-$id  --model=sonnet --dangerously-skip-permissions -p -c "Thanks for the help. Please commit  feature/test-$id and push all changes, then create a PR."
fi
